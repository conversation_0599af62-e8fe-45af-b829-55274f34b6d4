{"Version": 1, "WorkspaceRootPath": "D:\\SignalR\\SignalR-Backend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C11D26CF-F972-4385-A591-FDF2F73E5591}|SignalR-Backend\\SignalR-Backend.csproj|d:\\signalr\\signalr-backend\\signalr-backend\\models\\chatdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C11D26CF-F972-4385-A591-FDF2F73E5591}|SignalR-Backend\\SignalR-Backend.csproj|solutionrelative:signalr-backend\\models\\chatdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:1:0:{e8b06f52-6d01-11d2-aa7d-00c04f990343}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ChatDbContext.cs", "DocumentMoniker": "D:\\SignalR\\SignalR-Backend\\SignalR-Backend\\Models\\ChatDbContext.cs", "RelativeDocumentMoniker": "SignalR-Backend\\Models\\ChatDbContext.cs", "ToolTip": "D:\\SignalR\\SignalR-Backend\\SignalR-Backend\\Models\\ChatDbContext.cs", "RelativeToolTip": "SignalR-Backend\\Models\\ChatDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T11:00:55.47Z", "EditorCaption": ""}]}]}]}