using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SignalR_Backend.Models;
using System.ComponentModel.DataAnnotations;

namespace SignalR_Backend.Controllers
{
    /// <summary>
    /// Chat controller for managing chat messages and history
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class ChatController : ControllerBase
    {
        private readonly ChatDbContext _context;

        public ChatController(ChatDbContext context)
        {
            _context = context;
        }
        [HttpGet("messages")]
        [ProducesResponseType(typeof(IEnumerable<MessageDto>), 200)]
        [ProducesResponseType(typeof(object), 400)]
        public async Task<IActionResult> GetMessages([FromQuery] int limit = 50)
        {
            if (limit <= 0 || limit > 100)
            {
                return BadRequest(new { error = "Limit must be between 1 and 100" });
            }

            try
            {
                var messages = await _context.Messages
                    .Where(m => !m.IsDeleted)
                    .OrderByDescending(m => m.Timestamp)
                    .Take(limit)
                    .Select(m => new MessageDto
                    {
                        Id = m.Id,
                        User = m.User,
                        Content = m.Content,
                        Timestamp = m.Timestamp
                    })
                    .ToListAsync();

                // Reverse to get chronological order
                messages.Reverse();

                return Ok(new
                {
                    success = true,
                    count = messages.Count,
                    messages = messages
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, error = ex.Message });
            }
        }

        [HttpGet("stats")]
        [ProducesResponseType(typeof(object), 200)]
        public async Task<IActionResult> GetChatStats()
        {
            try
            {
                var totalMessages = await _context.Messages.CountAsync(m => !m.IsDeleted);
                var totalUsers = await _context.Messages
                    .Where(m => !m.IsDeleted)
                    .Select(m => m.User)
                    .Distinct()
                    .CountAsync();

                var latestMessage = await _context.Messages
                    .Where(m => !m.IsDeleted)
                    .OrderByDescending(m => m.Timestamp)
                    .FirstOrDefaultAsync();

                return Ok(new
                {
                    success = true,
                    totalMessages = totalMessages,
                    uniqueUsers = totalUsers,
                    latestMessageAt = latestMessage?.Timestamp,
                    hubEndpoint = "/chatHub"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, error = ex.Message });
            }
        }
    }

    public class MessageDto
    {
  
        public int Id { get; set; }

        public string User { get; set; } = string.Empty;

        public string Content { get; set; } = string.Empty;

        public DateTime Timestamp { get; set; }
    }
}
