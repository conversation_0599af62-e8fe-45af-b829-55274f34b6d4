using Microsoft.AspNetCore.SignalR;
using SignalR_Backend.Models;

namespace SignalR_Backend.Hubs
{
    public class ChatHub : Hub
    {
        private readonly ChatDbContext _context;

        public ChatHub(ChatDbContext context)
        {
            _context = context;
        }

        public async Task SendMessage(string user, string message)
        {
            // Save message to database
            var msg = new Message
            {
                User = user,
                Content = message,
                Timestamp = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };
            _context.Messages.Add(msg);
            await _context.SaveChangesAsync();

            // Broadcast message to all clients
            await Clients.All.SendAsync("ReceiveMessage", user, message, msg.Timestamp);
        }

        public async Task GetMessages()
        {
            var messages = _context.Messages
                .Where(m => !m.IsDeleted)
                .OrderBy(m => m.Timestamp)
                .Take(50)
                .Select(m => new { m.User, m.Content, m.Timestamp })
                .ToList();
            await Clients.Caller.SendAsync("LoadMessages", messages);
        }

        public override async Task OnConnectedAsync()
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "ChatRoom");
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, "ChatRoom");
            await base.OnDisconnectedAsync(exception);
        }
    }
}
