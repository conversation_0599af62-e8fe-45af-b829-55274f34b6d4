# React Frontend Integration Guide for SignalR Chat API

This guide provides step-by-step instructions for integrating your React frontend with the SignalR Chat API backend.

## 🚀 Quick Start

### Backend Information

- **API Base URL**: `http://localhost:5251`
- **SignalR Hub**: `http://localhost:5251/chatHub`
- **Swagger Documentation**: `http://localhost:5251/swagger`

## 📦 Required Dependencies

Install the necessary packages in your React project:

```bash
npm install @microsoft/signalr axios
# or
yarn add @microsoft/signalr axios
```

### Tailwind CSS Setup

Install and configure Tailwind CSS:

```bash
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

Update your `tailwind.config.js`:

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: {
          50: "#f0f9ff",
          500: "#3b82f6",
          600: "#2563eb",
          700: "#1d4ed8",
        },
      },
    },
  },
  plugins: [],
};
```

Add Tailwind directives to your `src/index.css`:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

## 🔧 Environment Setup

Create a `.env` file in your React project root:

```env
REACT_APP_API_BASE_URL=http://localhost:5251
REACT_APP_SIGNALR_HUB_URL=http://localhost:5251/chatHub
```

## 🏗️ Project Structure

Recommended folder structure for your React chat app:

```
src/
├── components/
│   ├── Chat/
│   │   ├── ChatContainer.jsx
│   │   ├── MessageList.jsx
│   │   ├── MessageInput.jsx
│   │   └── UserList.jsx
├── hooks/
│   ├── useSignalR.js
│   └── useChatAPI.js
├── services/
│   ├── signalRService.js
│   └── chatAPI.js
├── types/
│   └── chat.js
└── utils/
    └── constants.js
```

## 🔌 SignalR Service Implementation

### 1. Create SignalR Service (`src/services/signalRService.js`)

```javascript
import * as signalR from "@microsoft/signalr";

class SignalRService {
  constructor() {
    this.connection = null;
    this.isConnected = false;
  }

  async startConnection() {
    try {
      this.connection = new signalR.HubConnectionBuilder()
        .withUrl(process.env.REACT_APP_SIGNALR_HUB_URL, {
          skipNegotiation: true,
          transport: signalR.HttpTransportType.WebSockets,
        })
        .withAutomaticReconnect([0, 2000, 10000, 30000])
        .configureLogging(signalR.LogLevel.Information)
        .build();

      // Connection event handlers
      this.connection.onreconnecting(() => {
        console.log("SignalR: Attempting to reconnect...");
        this.isConnected = false;
      });

      this.connection.onreconnected(() => {
        console.log("SignalR: Reconnected successfully");
        this.isConnected = true;
      });

      this.connection.onclose(() => {
        console.log("SignalR: Connection closed");
        this.isConnected = false;
      });

      await this.connection.start();
      this.isConnected = true;
      console.log("SignalR: Connected successfully");

      return this.connection;
    } catch (error) {
      console.error("SignalR connection failed:", error);
      this.isConnected = false;
      throw error;
    }
  }

  async stopConnection() {
    if (this.connection) {
      await this.connection.stop();
      this.isConnected = false;
    }
  }

  async sendMessage(user, message) {
    if (this.isConnected && this.connection) {
      try {
        await this.connection.invoke("SendMessage", user, message);
      } catch (error) {
        console.error("Failed to send message:", error);
        throw error;
      }
    } else {
      throw new Error("SignalR connection not established");
    }
  }

  async getMessages() {
    if (this.isConnected && this.connection) {
      try {
        await this.connection.invoke("GetMessages");
      } catch (error) {
        console.error("Failed to get messages:", error);
        throw error;
      }
    }
  }

  onReceiveMessage(callback) {
    if (this.connection) {
      this.connection.on("ReceiveMessage", callback);
    }
  }

  onLoadMessages(callback) {
    if (this.connection) {
      this.connection.on("LoadMessages", callback);
    }
  }

  offReceiveMessage() {
    if (this.connection) {
      this.connection.off("ReceiveMessage");
    }
  }

  offLoadMessages() {
    if (this.connection) {
      this.connection.off("LoadMessages");
    }
  }

  getConnectionState() {
    return this.connection?.state || signalR.HubConnectionState.Disconnected;
  }
}

export default new SignalRService();
```

### 2. Create Chat API Service (`src/services/chatAPI.js`)

```javascript
import axios from "axios";

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL;

const chatAPI = axios.create({
  baseURL: `${API_BASE_URL}/api/chat`,
  headers: {
    "Content-Type": "application/json",
  },
});

export const getChatMessages = async (limit = 50) => {
  try {
    const response = await chatAPI.get(`/messages?limit=${limit}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch messages:", error);
    throw error;
  }
};

export const getChatStats = async () => {
  try {
    const response = await chatAPI.get("/stats");
    return response.data;
  } catch (error) {
    console.error("Failed to fetch chat stats:", error);
    throw error;
  }
};

export default chatAPI;
```

## 🎣 Custom React Hooks

### 3. Create SignalR Hook (`src/hooks/useSignalR.js`)

```javascript
import { useState, useEffect, useCallback } from "react";
import signalRService from "../services/signalRService";

export const useSignalR = () => {
  const [connectionStatus, setConnectionStatus] = useState("Disconnected");
  const [messages, setMessages] = useState([]);
  const [error, setError] = useState(null);

  const updateConnectionStatus = useCallback(() => {
    const state = signalRService.getConnectionState();
    const statusMap = {
      0: "Disconnected",
      1: "Connected",
      2: "Connecting",
      3: "Reconnecting",
    };
    setConnectionStatus(statusMap[state] || "Unknown");
  }, []);

  const connectToHub = useCallback(async () => {
    try {
      setError(null);
      setConnectionStatus("Connecting");

      await signalRService.startConnection();

      // Set up message handlers
      signalRService.onReceiveMessage((user, message, timestamp) => {
        setMessages((prev) => [
          ...prev,
          { user, message, timestamp, id: Date.now() },
        ]);
      });

      signalRService.onLoadMessages((messageHistory) => {
        setMessages(
          messageHistory.map((msg) => ({ ...msg, id: msg.id || Date.now() }))
        );
      });

      updateConnectionStatus();

      // Load initial messages
      await signalRService.getMessages();
    } catch (err) {
      setError(err.message);
      setConnectionStatus("Failed");
    }
  }, [updateConnectionStatus]);

  const disconnectFromHub = useCallback(async () => {
    try {
      signalRService.offReceiveMessage();
      signalRService.offLoadMessages();
      await signalRService.stopConnection();
      updateConnectionStatus();
    } catch (err) {
      setError(err.message);
    }
  }, [updateConnectionStatus]);

  const sendMessage = useCallback(async (user, message) => {
    try {
      setError(null);
      await signalRService.sendMessage(user, message);
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, []);

  useEffect(() => {
    connectToHub();

    return () => {
      disconnectFromHub();
    };
  }, [connectToHub, disconnectFromHub]);

  return {
    connectionStatus,
    messages,
    error,
    sendMessage,
    connectToHub,
    disconnectFromHub,
  };
};
```

### 4. Create Chat API Hook (`src/hooks/useChatAPI.js`)

```javascript
import { useState, useEffect } from "react";
import { getChatMessages, getChatStats } from "../services/chatAPI";

export const useChatAPI = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);
      const statsData = await getChatStats();
      setStats(statsData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchMessages = async (limit = 50) => {
    try {
      setLoading(true);
      setError(null);
      const messagesData = await getChatMessages(limit);
      return messagesData;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return {
    stats,
    loading,
    error,
    fetchStats,
    fetchMessages,
  };
};
```

## 🧩 React Components

### 5. Main Chat Container (`src/components/Chat/ChatContainer.jsx`)

```jsx
import React, { useState } from "react";
import { useSignalR } from "../../hooks/useSignalR";
import { useChatAPI } from "../../hooks/useChatAPI";
import MessageList from "./MessageList";
import MessageInput from "./MessageInput";

const ChatContainer = () => {
  const [currentUser, setCurrentUser] = useState("");
  const [isUserSet, setIsUserSet] = useState(false);

  const {
    connectionStatus,
    messages,
    error: signalRError,
    sendMessage,
  } = useSignalR();

  const { stats, loading, error: apiError } = useChatAPI();

  const handleUserSubmit = (username) => {
    if (username.trim()) {
      setCurrentUser(username.trim());
      setIsUserSet(true);
    }
  };

  const handleSendMessage = async (message) => {
    if (message.trim() && currentUser) {
      try {
        await sendMessage(currentUser, message.trim());
      } catch (error) {
        console.error("Failed to send message:", error);
      }
    }
  };

  const getConnectionStatusStyle = () => {
    switch (connectionStatus) {
      case "Connected":
        return "text-green-500";
      case "Connecting":
        return "text-yellow-500";
      case "Reconnecting":
        return "text-yellow-500";
      case "Failed":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  if (!isUserSet) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 w-full max-w-md">
          <div className="text-center mb-6">
            <div className="flex items-center justify-center mb-4">
              <div className="w-12 h-12 bg-black rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-xl">S</span>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 ml-3">
                SignalR Chat
              </h1>
            </div>
            <p className="text-gray-600">
              Enter your username to join the chat
            </p>
          </div>

          <form
            onSubmit={(e) => {
              e.preventDefault();
              const username = e.target.username.value;
              handleUserSubmit(username);
            }}
            className="space-y-4"
          >
            <div>
              <input
                name="username"
                type="text"
                placeholder="Enter your username"
                required
                maxLength={100}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-colors"
              />
            </div>
            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Join Chat
            </button>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center mr-3">
              <span className="text-white font-bold text-sm">S</span>
            </div>
            <h1 className="text-xl font-bold text-gray-900">SignalR Chat</h1>
          </div>

          <div className="flex items-center space-x-4 text-sm">
            <span className={`flex items-center ${getConnectionStatusStyle()}`}>
              <span className="w-2 h-2 rounded-full bg-current mr-2"></span>
              {connectionStatus}
            </span>
            <span className="text-gray-600">{currentUser}</span>
            {stats && (
              <span className="text-gray-500 hidden sm:block">
                {stats.totalMessages} messages
              </span>
            )}
          </div>
        </div>
      </header>

      {/* Error Message */}
      {(signalRError || apiError) && (
        <div className="max-w-4xl mx-auto px-4 py-2">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm">{signalRError || apiError}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Chat Content */}
      <div className="max-w-4xl mx-auto h-[calc(100vh-80px)] flex flex-col">
        <MessageList messages={messages} currentUser={currentUser} />
        <MessageInput
          onSendMessage={handleSendMessage}
          disabled={connectionStatus !== "Connected"}
          loading={loading}
        />
      </div>
    </div>
  );
};

export default ChatContainer;
```

### 6. Message List Component (`src/components/Chat/MessageList.jsx`)

```jsx
import React, { useEffect, useRef } from "react";

const MessageList = ({ messages, currentUser }) => {
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="message-list">
      {messages.length === 0 ? (
        <div className="no-messages">
          No messages yet. Start the conversation!
        </div>
      ) : (
        messages.map((message) => (
          <div
            key={message.id}
            className={`message ${
              message.user === currentUser ? "own-message" : "other-message"
            }`}
          >
            <div className="message-header">
              <span className="message-user">{message.user}</span>
              <span className="message-time">
                {formatTimestamp(message.timestamp)}
              </span>
            </div>
            <div className="message-content">
              {message.content || message.message}
            </div>
          </div>
        ))
      )}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
```

### 7. Message Input Component (`src/components/Chat/MessageInput.jsx`)

```jsx
import React, { useState } from "react";

const MessageInput = ({ onSendMessage, disabled, loading }) => {
  const [message, setMessage] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    if (message.trim() && !disabled) {
      onSendMessage(message);
      setMessage("");
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <form className="message-input-form" onSubmit={handleSubmit}>
      <div className="input-container">
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={disabled ? "Connecting..." : "Type your message..."}
          disabled={disabled || loading}
          maxLength={1000}
          rows={1}
          className="message-input"
        />
        <button
          type="submit"
          disabled={disabled || loading || !message.trim()}
          className="send-button"
        >
          {loading ? "..." : "Send"}
        </button>
      </div>
      <div className="input-info">
        <span className="char-count">{message.length}/1000</span>
      </div>
    </form>
  );
};

export default MessageInput;
```

## 🎨 CSS Styling

### 8. Chat Container Styles (`src/components/Chat/ChatContainer.css`)

```css
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 800px;
  margin: 0 auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.chat-header {
  background: #2196f3;
  color: white;
  padding: 1rem;
  border-bottom: 1px solid #1976d2;
}

.chat-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.chat-info {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  flex-wrap: wrap;
}

.connection-status {
  font-weight: bold;
}

.user-info,
.stats {
  opacity: 0.9;
}

.chat-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 2rem;
}

.chat-login h2 {
  margin-bottom: 1rem;
  color: #333;
}

.chat-login form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  max-width: 300px;
}

.chat-login input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.chat-login button {
  padding: 0.75rem;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
}

.chat-login button:hover {
  background: #1976d2;
}

.error-message {
  background: #ffebee;
  color: #c62828;
  padding: 0.75rem;
  border-left: 4px solid #f44336;
  margin: 0.5rem;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: #fafafa;
}

.no-messages {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
}

.message {
  margin-bottom: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  max-width: 70%;
}

.own-message {
  background: #e3f2fd;
  margin-left: auto;
  border-bottom-right-radius: 4px;
}

.other-message {
  background: white;
  border: 1px solid #e0e0e0;
  border-bottom-left-radius: 4px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
  font-size: 0.8rem;
}

.message-user {
  font-weight: bold;
  color: #1976d2;
}

.message-time {
  color: #666;
}

.message-content {
  word-wrap: break-word;
  line-height: 1.4;
}

.message-input-form {
  border-top: 1px solid #e0e0e0;
  padding: 1rem;
  background: white;
}

.input-container {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 20px;
  resize: none;
  font-family: inherit;
  font-size: 1rem;
  max-height: 100px;
  min-height: 40px;
}

.message-input:focus {
  outline: none;
  border-color: #2196f3;
}

.send-button {
  padding: 0.75rem 1.5rem;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 1rem;
  min-width: 60px;
}

.send-button:hover:not(:disabled) {
  background: #1976d2;
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.input-info {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.25rem;
}

.char-count {
  font-size: 0.75rem;
  color: #666;
}
```

## 🚀 Usage Example

### 9. Main App Component (`src/App.js`)

```jsx
import React from "react";
import ChatContainer from "./components/Chat/ChatContainer";
import "./App.css";

function App() {
  return (
    <div className="App">
      <ChatContainer />
    </div>
  );
}

export default App;
```

### 10. Basic App Styles (`src/App.css`)

```css
.App {
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
    "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  background: #f5f5f5;
}
```

## 🔧 Configuration & Testing

### Start Your React App

```bash
# Make sure your backend is running on http://localhost:5251
npm start
# or
yarn start
```

### Test the Integration

1. **Backend Check**: Verify backend is running at `http://localhost:5251/swagger`
2. **Frontend Check**: Open React app (usually `http://localhost:3000`)
3. **Connection Test**: Check browser console for SignalR connection logs
4. **Message Test**: Send messages between multiple browser tabs

## 🐛 Troubleshooting

### Common Issues

#### 1. CORS Errors

```
Access to XMLHttpRequest at 'http://localhost:5251/chatHub/negotiate'
from origin 'http://localhost:3000' has been blocked by CORS policy
```

**Solution**: Ensure backend CORS is configured for your React app URL:

```csharp
// In Program.cs
policy.WithOrigins("http://localhost:3000", "https://localhost:3000")
```

#### 2. SignalR Connection Failed

```
Error: Failed to start the connection: Error: WebSocket failed to connect
```

**Solutions**:

- Check if backend is running on correct port
- Verify SignalR hub URL in environment variables
- Try different transport types:

```javascript
.withUrl(hubUrl, {
  transport: signalR.HttpTransportType.LongPolling
})
```

#### 3. Messages Not Appearing

- Check browser console for JavaScript errors
- Verify message event handlers are properly set up
- Ensure connection is established before sending messages

#### 4. Database Connection Issues

- Verify SQL Server is running
- Check connection string in `appsettings.json`
- Run database migrations if needed

### Debug Tips

1. **Enable SignalR Logging**:

```javascript
.configureLogging(signalR.LogLevel.Debug)
```

2. **Monitor Network Tab**: Check browser DevTools Network tab for failed requests

3. **Backend Logs**: Monitor backend console for SignalR connection events

4. **Test API Endpoints**: Use Swagger UI to test REST endpoints independently

## 📱 Mobile Responsiveness

Add these CSS media queries for mobile support:

```css
@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
    border-radius: 0;
    border: none;
  }

  .chat-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .message {
    max-width: 85%;
  }

  .input-container {
    flex-direction: column;
    gap: 0.5rem;
  }

  .message-input {
    border-radius: 8px;
  }

  .send-button {
    border-radius: 8px;
    align-self: flex-end;
  }
}
```

## 🔒 Security Considerations

1. **Input Validation**: Always validate user input on both client and server
2. **XSS Prevention**: Sanitize message content before displaying
3. **Rate Limiting**: Implement message rate limiting to prevent spam
4. **Authentication**: Add user authentication for production use
5. **HTTPS**: Use HTTPS in production environments

## 🚀 Production Deployment

### Environment Variables for Production

```env
REACT_APP_API_BASE_URL=https://your-api-domain.com
REACT_APP_SIGNALR_HUB_URL=https://your-api-domain.com/chatHub
```

### Build for Production

```bash
npm run build
# or
yarn build
```

## 📚 Additional Resources

- [SignalR JavaScript Client Documentation](https://docs.microsoft.com/en-us/aspnet/core/signalr/javascript-client)
- [React Hooks Documentation](https://reactjs.org/docs/hooks-intro.html)
- [Axios Documentation](https://axios-http.com/docs/intro)

## 🎯 Next Steps

1. Add user authentication
2. Implement message persistence
3. Add file/image sharing
4. Create chat rooms/channels
5. Add typing indicators
6. Implement message reactions
7. Add push notifications

---

**Happy Coding! 🚀**

For questions or issues, refer to the Swagger documentation at `http://localhost:5251/swagger` or check the browser console for detailed error messages.
