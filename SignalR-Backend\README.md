# SignalR Chat API Backend

A real-time chat application backend built with ASP.NET Core and SignalR, featuring REST API endpoints and WebSocket communication.

## 🚀 Features

- **Real-time messaging** with SignalR WebSockets
- **REST API** for chat history and statistics
- **Entity Framework Core** with SQL Server
- **Swagger/OpenAPI** documentation
- **CORS support** for React frontend
- **Automatic reconnection** handling
- **Message persistence** in database

## 🛠️ Technology Stack

- **ASP.NET Core 9.0**
- **SignalR** for real-time communication
- **Entity Framework Core** for data access
- **SQL Server** database
- **Swashbuckle** for API documentation

## 📦 Prerequisites

- .NET 9.0 SDK
- SQL Server (LocalDB or full instance)
- Visual Studio 2022 or VS Code

## 🚀 Getting Started

### 1. Clone and Setup

```bash
git clone <your-repo-url>
cd SignalR-Backend/SignalR-Backend
```

### 2. Database Setup

Update the connection string in `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.;Database=SignalRDb;Trusted_Connection=True;TrustServerCertificate=True;"
  }
}
```

### 3. Install Dependencies

```bash
dotnet restore
```

### 4. Run Database Migrations

```bash
dotnet ef database update
```

### 5. Start the Application

```bash
dotnet run --urls "http://localhost:5251"
```

## 📚 API Documentation

Once running, access the Swagger documentation at:
- **Swagger UI**: `http://localhost:5251/swagger`
- **OpenAPI JSON**: `http://localhost:5251/swagger/v1/swagger.json`

## 🔌 API Endpoints

### Chat Controller (`/api/chat`)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/chat/messages?limit=50` | Get chat message history |
| GET | `/api/chat/stats` | Get chat statistics |

### SignalR Hub (`/chatHub`)

| Method | Description |
|--------|-------------|
| `SendMessage(user, message)` | Send a message to all connected clients |
| `GetMessages()` | Load message history for the current connection |

#### Hub Events

| Event | Parameters | Description |
|-------|------------|-------------|
| `ReceiveMessage` | `user, message, timestamp` | Fired when a new message is received |
| `LoadMessages` | `messageArray` | Fired when message history is loaded |

## 🔧 Configuration

### CORS Settings

The application is configured to allow requests from:
- `http://localhost:3000` (React development server)
- `https://localhost:3000`
- `http://localhost:3001`

To add more origins, update `Program.cs`:

```csharp
policy.WithOrigins("http://localhost:3000", "your-frontend-url")
```

### Database Configuration

The application uses Entity Framework Core with SQL Server. The database context is configured in `Program.cs` and models are in the `Models` folder.

## 🏗️ Project Structure

```
SignalR-Backend/
├── Controllers/
│   ├── ChatController.cs      # REST API endpoints
│   └── WeatherForecastController.cs
├── Hubs/
│   └── ChatHub.cs            # SignalR hub
├── Models/
│   ├── ChatDbContext.cs      # EF Core context
│   └── Message.cs            # Message entity
├── Properties/
│   └── launchSettings.json   # Launch configuration
├── Program.cs                # Application startup
├── appsettings.json          # Configuration
└── REACT_INTEGRATION_GUIDE.md # Frontend integration guide
```

## 🧪 Testing

### Using Swagger UI

1. Navigate to `http://localhost:5251/swagger`
2. Test the `/api/chat/stats` endpoint
3. Test the `/api/chat/messages` endpoint

### Using SignalR

1. Use the React frontend (see `REACT_INTEGRATION_GUIDE.md`)
2. Or use a SignalR client library in your preferred language

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**: Change the port in `launchSettings.json`
2. **Database connection failed**: Verify SQL Server is running and connection string is correct
3. **CORS errors**: Ensure your frontend URL is added to the CORS policy

### Logs

Monitor the console output for:
- SignalR connection events
- Database queries
- Error messages

## 🔒 Security Notes

- Input validation is implemented on API endpoints
- CORS is configured for specific origins
- Consider adding authentication for production use

## 🚀 Deployment

### Development
```bash
dotnet run --urls "http://localhost:5251"
```

### Production
```bash
dotnet publish -c Release
```

## 📱 Frontend Integration

See the comprehensive React integration guide: [`REACT_INTEGRATION_GUIDE.md`](./REACT_INTEGRATION_GUIDE.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

---

**Happy Coding! 🚀**

For detailed frontend integration instructions, see [`REACT_INTEGRATION_GUIDE.md`](./REACT_INTEGRATION_GUIDE.md)
