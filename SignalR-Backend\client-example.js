// Example React SignalR Client Setup
// Install: npm install @microsoft/signalr

import * as signalR from "@microsoft/signalr";
import { useState, useEffect, useRef } from 'react';

const ChatComponent = () => {
    const [connection, setConnection] = useState(null);
    const [messages, setMessages] = useState([]);
    const [user, setUser] = useState('');
    const [message, setMessage] = useState('');
    const [connectionStatus, setConnectionStatus] = useState('Disconnected');

    useEffect(() => {
        // Create connection
        const newConnection = new signalR.HubConnectionBuilder()
            .withUrl("http://localhost:5251/chatHub", {
                skipNegotiation: true,
                transport: signalR.HttpTransportType.WebSockets
            })
            .withAutomaticReconnect()
            .build();

        // Set up event handlers BEFORE starting connection
        newConnection.on("ReceiveMessage", (user, message, timestamp) => {
            setMessages(prev => [...prev, { user, message, timestamp }]);
        });

        newConnection.on("LoadMessages", (messageHistory) => {
            setMessages(messageHistory);
        });

        // Start connection
        const startConnection = async () => {
            try {
                await newConnection.start();
                setConnectionStatus('Connected');
                setConnection(newConnection);
                
                // Load message history after connection is established
                await newConnection.invoke("GetMessages");
                
            } catch (error) {
                console.error('Connection failed: ', error);
                setConnectionStatus('Failed');
            }
        };

        startConnection();

        // Cleanup on unmount
        return () => {
            if (newConnection) {
                newConnection.stop();
            }
        };
    }, []);

    const sendMessage = async () => {
        // IMPORTANT: Check connection state before invoking
        if (connection && connection.state === signalR.HubConnectionState.Connected) {
            try {
                await connection.invoke("SendMessage", user, message);
                setMessage(''); // Clear input after sending
            } catch (error) {
                console.error('Send message failed: ', error);
            }
        } else {
            alert('Connection not established. Please wait or refresh the page.');
        }
    };

    return (
        <div>
            <div>Status: {connectionStatus}</div>
            <div>
                <input 
                    type="text" 
                    placeholder="Your name" 
                    value={user} 
                    onChange={(e) => setUser(e.target.value)} 
                />
                <input 
                    type="text" 
                    placeholder="Message" 
                    value={message} 
                    onChange={(e) => setMessage(e.target.value)} 
                />
                <button 
                    onClick={sendMessage}
                    disabled={connectionStatus !== 'Connected'}
                >
                    Send
                </button>
            </div>
            <div>
                {messages.map((msg, index) => (
                    <div key={index}>
                        <strong>{msg.user}:</strong> {msg.message}
                        <small> ({new Date(msg.timestamp).toLocaleTimeString()})</small>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default ChatComponent;
